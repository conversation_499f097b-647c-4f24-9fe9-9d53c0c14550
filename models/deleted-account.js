const mongoose = require('mongoose');

const schema = new mongoose.Schema({
  createdAt: { type: Date },
  deletedDate: { type: Date },
  country: { type: String },
  state: { type: String },
  city: { type: String },
  signupCountry: { type: String },
  googlePlayCountry: { type: String },
  personality: {
    mbti: { type: String },
    EI: { type: Number },
    NS: { type: Number },
    FT: { type: Number },
    JP: { type: Number },
    TA: { type: Number },
  },
  enneagram: { type: String },
  gender: { type: String },
  age: { type: Number },
  height: { type: Number },
  horoscope: { type: String },
  relationshipStatus: { type: String },
  datingSubPreferences:{ type: String },
  relationshipType: { type: String },
  moreAboutUser: {
    exercise: { type: String },
    educationLevel: { type: String },
    drinking: { type: String },
    smoking: { type: String },
    kids: { type: String },
    religion: { type: String },
  },
  preferences: {
    minAge: { type: Number },
    maxAge: { type: Number },
    minHeight: { type: Number },
    maxHeight: { type: Number },
    personality: [ { type: String } ],
    dating: [ { type: String } ],
    friends: [ { type: String } ],
    local: { type: Boolean },
    global: { type: Boolean },
    showVerifiedOnly: { type: Boolean },
    countries: [ { type: String } ],
    interestNames: [ { type: String } ],
    enneagrams: [ { type: String } ],
    horoscopes: [ { type: String } ],
    languages: [ { type: String } ],
    exercise: [ { type: String } ],
    educationLevel: [ { type: String } ],
    drinking: [ { type: String } ],
    smoking: [ { type: String } ],
    relationshipStatus: [ { type: String } ],
    datingSubPreferences: [ { type: String } ],
    relationshipType: [ { type: String } ],
    kids: [ { type: String } ],
    religion: [ { type: String } ],
    keywords: [ { type: String } ],
  },
  metrics: {
    numReferralsMade: { type: Number },
    numActionsSent: { type: Number },
    numActionsReceived: { type: Number },
    numLikesSent: { type: Number },
    numPassesSent: { type: Number },
    numLocalLikesSent: { type: Number },
    numLocalPassesSent: { type: Number },
    numGlobalLikesSent: { type: Number },
    numGlobalPassesSent: { type: Number },
    numApprovalsSent: { type: Number },
    numRejectionsSent: { type: Number },
    numUnmatchesSent: { type: Number },
    numBlocksSent: { type: Number },
    numMessagesSent: { type: Number },
    numLikesReceived: { type: Number },
    numPassesReceived: { type: Number },
    numApprovalsReceived: { type: Number },
    numRejectionsReceived: { type: Number },
    numUnmatchesReceived: { type: Number },
    numBlocksReceived: { type: Number },
    numMessagesReceived: { type: Number },
    numNotifications: { type: Number },
    numNotificationErrors: { type: Number },
    numMatches: { type: Number },
    numMatchesFirstMessageSent: { type: Number },
    numMatchesFirstMessageReceived: { type: Number },
    numMatchesReplySent: { type: Number },
    numMatchesReplyReceived: { type: Number },
    numMatchesBothMessaged: { type: Number },
    numDMSent: { type: Number },
    numDMSentFromSwiping: { type: Number },
    numDMSentFromSocial: { type: Number },
    numDMSentFromStories: { type: Number },
    numDMReceived: { type: Number },
    numSuperLikesPurchased: { type: Number },
    numSuperLikesSent: { type: Number },
    numNeuronsPurchased: { type: Number },
    numNeuronsUsed: { type: Number },
    numMinutesUntilFirstLikeReceived: { type: Number },
    numMinutesUntilFirstMatch: { type: Number },
    numMinutesUntilFirstMessageReceived: { type: Number },
    numMinutesUntilFirstPost: { type: Number },
    numPersonalizedLikesSent: { type: Number },
    numPersonalizedPassesSent: { type: Number },
    numSessions: { type: Number },
    numEngagedSessions: { type: Number },
    numSecondsEngagementTime: { type: Number },
    avgSecondsEngagementTime: { type: Number },
    numDeleteAccountAttempts: { type: Number },
    numDeleteAccountAttemptsCancelled: { type: Number },
    numFeedbackSubmitted: { type: Number },
    gotFreeTrial: { type: Boolean },
    madePurchase: { type: Boolean },
    numPurchases: { type: Number },
    daysOnPlatformBeforePurchase: { type: Number },
    numCoinPurchases: { type: Number },
    numCoinsPurchased: { type: Number },
    numStripePurchases: { type: Number },
    numRefunds: { type: Number },
    revenueRefunded: { type: Number },
    revenue: { type: Number },
    coinRevenue: { type: Number },
    numStickerPackPurchases: { type: Number },
    stickerPackRevenue: { type: Number },
    numSuperLikePurchases: { type: Number },
    superLikeRevenue: { type: Number },
    numNeuronPurchases: { type: Number },
    neuronRevenue: { type: Number, },
    stripeRevenue: { type: Number },
    saleRevenue: { type: Number },
    numSalePurchases: { type: Number },
    numCoinAdsWatched: { type: Number },
    numCoinsEarnedFromAds: { type: Number },
    numViewLastSeenPurchased: { type: Number },
    purchasedPremiumFrom: { type: String },
    purchasedCoinsFrom: { type: String },
    lastSeen: { type: Date },
    lastSeenAndroid: { type: Date },
    lastSeenIos: { type: Date },
    lastSeenWeb: { type: Date },
    numFollowers: { type: Number },
    numFollowing: { type: Number },
    numFollowRequests: { type: Number },
    numProfileViews: { type: Number },
    numPendingReports: { type: Number },
    numTotalReports: { type: Number },
    numAwardsReceived: { type: Number },
    numAwardsSent: { type: Number },
    numQuestions: { type: Number },
    numComments: { type: Number },
    numPostLikesReceived: { type: Number },
    numPostLikesSent: { type: Number },
    numPostUnlikesSent: { type: Number },
    numPostShares: { type: Number },
    numPostClicks: { type: Number },
    numSecondsReadingOnFeed: { type: Number },
    numSecondsReadingComments: { type: Number },
    receivedFreeTrialFromBackend: { type: Boolean },
    receivedFreeTrialFromBackendExpirationDate: { type: Date },
    promptedForAppRating: { type: Boolean },
    numTipEmailsSent: { type: Number },
    numFlashSales: { type: Number },
    retention: [ { type: Boolean } ],
    activeDates: [ { type: Date } ],
    karmaFromLikingPosts: { type: Number },
    karmaFromFirstToMessage: { type: Number },
    had30DayInactivity: { type: Number },
    resurrected: { type: Number },
    lastNewJoinNotifiedAt: { type: Date },
  },
  events: {
    enterDeleteAccountFlow: { type: Number },
    enterCoinsPage: { type: Number },
    enterSocialPage: { type: Number },
    referralLinkCreated: { type: Number },
    enterInvitePage: { type: Number },
    sharePersonality: { type: Number },
    friendPosted: { type: Number },
    newUsersJoinedCity: { type: Number },
    tap_dimension_icon: { type: Number },
    change_dimension: { type: Number },
  },
  verification: {
    status: { type: String },
  },
  shadowBanned: { type: Boolean },
  bannedReason: { type: String },
  bannedBy: { type: String },
  bannedDate: { type: Date },
  signupSource: { type: String },
  webSignupCategory: { type: String },
  webSignupPage: { type: String },
  webFirstVisitCategory: { type: String },
  webFirstVisitPage: { type: String },
  webFirstVisitReferringDomain: { type: String },
  webFirstVisitReferringURL: { type: String },
  os: { type: String },
  osVersion: { type: String },
  deviceLanguage: { type: String },
  timezone: { type: String },
  ipData: {
    _id: false,
    date: Date,
    city: String,
    region: String,
    country: String,
    countryCode: String,
    location: mongoose.Schema.Types.Point,
    metro: Number,
    area: Number,
    timezone: String,
  },
  locale: { type: String },
  interestNames: [ { type: String } ],
  karma: { type: Number },
  languages: [ { type: String } ],
  ethnicities: [ { type: String } ],
}, {
  versionKey: false,
});

schema.index({ deletedDate: 1 }, { expireAfterSeconds: 3 * 365 * 24 * 60 * 60 });

// Export schema
// =============================================================================
module.exports = mongoose.model('DeletedAccount', schema);
