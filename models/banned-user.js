const mongoose = require('mongoose');

const bannedUserSchema = new mongoose.Schema({
  createdAt: { type: Date, default: () => new Date() },
  user: { type: String, ref: 'User' },
  userData:Object,
  messages: [Object],
  questions: [Object],
  comments: [Object],
  faceIds: [String],
  deletedAt: { type: Date },
});

bannedUserSchema.index({
  user: 1,
});

bannedUserSchema.index({
  'userData.email': 1,
});

bannedUserSchema.index({
  'userData.phoneNumber': 1,
});

bannedUserSchema.index({
  'userData.deviceId': 1,
});

bannedUserSchema.index(
  { deletedAt: 1 },
  { partialFilterExpression: { deletedAt: { $exists: true } } },
);

// Export schema =====================================================================================================================================================================
module.exports = mongoose.model('BannedUser', bannedUserSchema);
