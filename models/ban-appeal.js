const mongoose = require('mongoose');

const schema = new mongoose.Schema({
  createdAt: { type: Date, default: Date.now },
  user: { type: String, ref: 'User' },
  comment: { type: String },
  reviewedBy: { type: String, ref: 'User' },
  reviewedAt: { type: Date },
  decision: { type: String, enum: [ 'approved', 'rejected' ] },
  notes: { type: String },
});

schema.index(
  {
    createdAt: 1,
  },
  { partialFilterExpression: { decision: { $exists: false } } },
);

// Export schema =====================================================================================================================================================================
module.exports = mongoose.model('BanAppeal', schema);
