const mongoose = require('mongoose');

const reportSchema = new mongoose.Schema({
  createdAt: { type: Date, default: () => Date.now() },
  updatedAt: {
    type: Date,
    default() {
      return this.createdAt;
    },
  },
  reportedUser: { type: String, ref: 'User' },
  reportedBy: { type: String, ref: 'User' },
  reason: [{
    type: String,
    trim: true,
    maxlength: 2000,
  }],
  comment: {
    type: String,
    default: '',
    trim: true,
    maxlength: 10000,
  },
  status: {
    type: String,
    enum: ['needs review', 'dismissed', 'verified'],
    default: 'needs review',
  },
  handledBy: { type: String, ref: 'User' },
  adminNotes: { type: String },
  grantCoinAwardAt: { type: Date },
  coinAwardGrantedAt: { type: Date },
  messages: [{}],
  scammerImageDetection: {
    fullMatchingImages: [{
      url: { type: String },
      score: { type: Number },
    }],
    partialMatchingImages: [{
      url: { type: String },
      score: { type: Number },
    }],
  },
  profilePictureSameAsBannedUser: { type: String, ref: 'User' },
  openai: {
    prompt: { type: String },
    output: { type: String },
    reasoning_content: { type: String },
    promptTokens: { type: Number },
    outputTokens: { type: Number },
    isError: { type: Boolean },
    errorMessage: { type: String },
    cost: { type: Number },
    provider: { type: String },
    model: { type: String },
    processingTime: { type: Number },
    ban: { type: Boolean },
    banReason: { type: String },
    violationLocation: { type: String },
    decision: { type: String },
    infringingText: { type: [ { type: String } ], default: undefined },
    infringingPictures: { type: [ { type: Number } ], default: undefined },
    infringingPictureKeys: { type: [ { type: String } ], default: undefined },
    infringingTextFoundInName: { type: Boolean },
  },
  openaiFollowUp: [{
    date: { type: Date },
    prompt: { type: String },
    output: { type: String },
    promptTokens: { type: Number },
    outputTokens: { type: Number },
    isError: { type: Boolean },
    errorMessage: { type: String },
    cost: { type: Number },
    provider: { type: String },
    model: { type: String },
    processingTime: { type: Number },
    ban: { type: Boolean },
    banReason: { type: String },
    violationLocation: { type: String },
    decision: { type: String },
  }],
});

reportSchema.index({
  reportedUser: 1,
});
reportSchema.index({
  reportedBy: 1,
  createdAt: 1,
});

reportSchema.index({
  status: 1,
  updatedAt: -1,
});

reportSchema.index(
  { grantCoinAwardAt: 1 },
  { partialFilterExpression: { grantCoinAwardAt: { $exists: true } } },
);

// Define methods
// ============================================================================

reportSchema.statics.updateReports = async function (query, update) {
  if (query.reportedUser) {
    const reportedUser = await mongoose.model('User').findOne({ _id: query.reportedUser });
    if (reportedUser && reportedUser.metrics.numPendingReports > 0) {
      reportedUser.metrics.numPendingReports = 0;
      await reportedUser.save();
    }
  }
  query.status = 'needs review';
  update.updatedAt = Date.now();
  await this.updateMany(
    query,
    update,
  );
};

reportSchema.statics.dismissReports = async function (query, handledBy) {
  await this.updateReports(
    query,
    { status: 'dismissed', handledBy },
  );
};

reportSchema.statics.verifyReports = async function (query, handledBy) {
  await this.updateReports(
    query,
    { status: 'verified', handledBy },
  );
};

// Export schema =====================================================================================================================================================================
module.exports = mongoose.model('Report', reportSchema);
