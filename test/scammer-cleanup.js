const { expect, assert } = require('chai');
const request = require('supertest');
const { app } = require('./common');
const sinon = require('sinon');
const User = require('../models/user');
const ScammerCleanup = require('../models/scammer-cleanup');
const SupportAgentAccuracy = require('../models/support-accuracy');
const constants = require('../lib/constants');

it('scammer cleanup', async () => {

  // create admin users
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  user = await User.findOne({ _id: 1 });
  user.admin = true;
  user.adminPermissions = { support: true };
  await user.save();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  user = await User.findOne({ _id: 2 });
  user.admin = true;
  user.adminPermissions = { support: true };
  await user.save();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  user = await User.findOne({ _id: 3 });
  user.admin = true;
  user.adminPermissions = { support: true, manager: true };
  await user.save();

  // create other users
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 100);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 101);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 102);
  expect(res.status).to.equal(200);

  // seed data
  await ScammerCleanup.insertMany([
    {
      user: '100',
      verificationPhoto: '100',
      review1: { queue: 1 },
      review2: { queue: 2 },
    },
    {
      user: '101',
      verificationPhoto: '101',
      review1: { queue: 2 },
      review2: { queue: 1 },
    },
    {
      user: '102',
      verificationPhoto: '102',
      review1: { queue: 2 },
      review2: { queue: 3 },
    },
  ]);

  // test getting profiles for review
  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 1 })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  console.log(res.body.profiles);
  expect(res.body.profiles.length).to.equal(2);
  expect(res.body.profiles).to.containSubset([{ user: '100', verificationPhoto: '100' }]);
  expect(res.body.profiles).to.containSubset([{ user: '101', verificationPhoto: '101' }]);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 2 })
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(3);
  expect(res.body.profiles).to.containSubset([{ user: '100', verificationPhoto: '100' }]);
  expect(res.body.profiles).to.containSubset([{ user: '101', verificationPhoto: '101' }]);
  expect(res.body.profiles).to.containSubset([{ user: '102', verificationPhoto: '102' }]);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 3 })
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles).to.containSubset([{ user: '102', verificationPhoto: '102' }]);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 4 })
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/reviewFinal')
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  // incorrect queue number should be rejected
  res = await request(app)
    .put('/v1/admin/scammerCleanup/review/decision')
    .send({ queue: 3, user: '100', decision: 'ban' })
    .set('authorization', 1);
  expect(res.status).to.equal(422);

  // user 1 decides ban for user 100
  // user 100 removed from queue 1
  // final review queue empty
  // user 100 not banned yet
  res = await request(app)
    .put('/v1/admin/scammerCleanup/review/decision')
    .send({ queue: 1, user: '100', decision: 'ban' })
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 1 })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles).to.containSubset([{ user: '101', verificationPhoto: '101' }]);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 2 })
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(3);
  expect(res.body.profiles).to.containSubset([{ user: '100', verificationPhoto: '100' }]);
  expect(res.body.profiles).to.containSubset([{ user: '101', verificationPhoto: '101' }]);
  expect(res.body.profiles).to.containSubset([{ user: '102', verificationPhoto: '102' }]);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/reviewFinal')
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  user = await User.findOne({ _id: 100 });
  expect(user.shadowBanned).to.equal(false);
  expect(user.bannedReason).to.equal();
  expect(user.bannedNotes).to.equal();

  // user 1 cannot issue a second decision
  res = await request(app)
    .put('/v1/admin/scammerCleanup/review/decision')
    .send({ queue: 2, user: '100', decision: 'ban' })
    .set('authorization', 1);
  expect(res.status).to.equal(422);

  // another admin cannot send another decision for the same queue number
  res = await request(app)
    .put('/v1/admin/scammerCleanup/review/decision')
    .send({ queue: 1, user: '100', decision: 'dismiss' })
    .set('authorization', 2);
  expect(res.status).to.equal(422);

  // incorrect queue number should be rejected
  res = await request(app)
    .put('/v1/admin/scammerCleanup/review/decision')
    .send({ queue: 1, user: '100', decision: 'ban' })
    .set('authorization', 2);
  expect(res.status).to.equal(422);

  // user 2 also decides ban for user 100
  // user 100 removed from queue 2
  // final review queue empty
  // user 100 banned
  res = await request(app)
    .put('/v1/admin/scammerCleanup/review/decision')
    .send({ queue: 2, user: '100', decision: 'ban' })
    .set('authorization', 2);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 1 })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles).to.containSubset([{ user: '101', verificationPhoto: '101' }]);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 2 })
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);
  expect(res.body.profiles).to.containSubset([{ user: '101', verificationPhoto: '101' }]);
  expect(res.body.profiles).to.containSubset([{ user: '102', verificationPhoto: '102' }]);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/reviewFinal')
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  user = await User.findOne({ _id: 100 });
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedReason).to.equal('scammer verification photo cleanup');
  expect(user.bannedNotes).to.equal('also reviewed by 1');
  expect(user.bannedBy).to.equal('2');

  // user 2 decides dismiss for user 101
  // user 101 removed from queue 2
  // final review queue empty
  // user 101 not banned
  res = await request(app)
    .put('/v1/admin/scammerCleanup/review/decision')
    .send({ queue: 2, user: '101', decision: 'dismiss' })
    .set('authorization', 2);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 1 })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles).to.containSubset([{ user: '101', verificationPhoto: '101' }]);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 2 })
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles).to.containSubset([{ user: '102', verificationPhoto: '102' }]);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/reviewFinal')
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  user = await User.findOne({ _id: 101 });
  expect(user.shadowBanned).to.equal(false);
  expect(user.bannedReason).to.equal();
  expect(user.bannedNotes).to.equal();

  // user 1 also decides dismiss for user 101
  // user 101 removed from queue 1
  // final review queue empty
  // user 100 not banned
  res = await request(app)
    .put('/v1/admin/scammerCleanup/review/decision')
    .send({ queue: 1, user: '101', decision: 'dismiss' })
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 1 })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 2 })
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles).to.containSubset([{ user: '102', verificationPhoto: '102' }]);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/reviewFinal')
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  user = await User.findOne({ _id: 101 });
  expect(user.shadowBanned).to.equal(false);
  expect(user.bannedReason).to.equal();
  expect(user.bannedNotes).to.equal();

  // user 3 decides dismiss for user 102
  // user 102 removed from queue 3
  // final review queue empty
  // user 100 not banned
  res = await request(app)
    .put('/v1/admin/scammerCleanup/review/decision')
    .send({ queue: 3, user: '102', decision: 'dismiss' })
    .set('authorization', 3);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 1 })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 2 })
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles).to.containSubset([{ user: '102', verificationPhoto: '102' }]);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 3 })
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/reviewFinal')
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  user = await User.findOne({ _id: 102 });
  expect(user.shadowBanned).to.equal(false);
  expect(user.bannedReason).to.equal();
  expect(user.bannedNotes).to.equal();

  // cannot submit decision for final review yet
  res = await request(app)
    .put('/v1/admin/scammerCleanup/reviewFinal/decision')
    .send({ queue: 3, user: '102', decision: 'dismiss' })
    .set('authorization', 3);
  expect(res.status).to.equal(422);

  // user 2 decides ban for user 102
  // user 102 removed from queue 2
  // user 2 moved to final review queue
  // user 102 not banned yet
  res = await request(app)
    .put('/v1/admin/scammerCleanup/review/decision')
    .send({ queue: 2, user: '102', decision: 'ban' })
    .set('authorization', 2);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 1 })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 2 })
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 3 })
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/reviewFinal')
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles[0].user).to.equal('102');
  expect(res.body.profiles[0].verificationPhoto).to.equal('102');

  user = await User.findOne({ _id: 102 });
  expect(user.shadowBanned).to.equal(false);
  expect(user.bannedReason).to.equal();
  expect(user.bannedNotes).to.equal();

  // submit ban for final review
  // user 102 removed from final review queue
  // user 102 banned
  res = await request(app)
    .put('/v1/admin/scammerCleanup/reviewFinal/decision')
    .send({ user: '102', decision: 'ban' })
    .set('authorization', 3);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/reviewFinal')
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  user = await User.findOne({ _id: 102 });
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedReason).to.equal('scammer verification photo cleanup');
  expect(user.bannedNotes).to.equal();
  expect(user.bannedBy).to.equal('3');
});

describe('double queue system for photo verification', async () => {
  const challengeId = '1110f6e4-a225-11ed-bf55-d33b0802ecdc';

  beforeEach(async () => {
    // create admin users
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    user = await User.findOne({ _id: 1 });
    user.admin = true;
    user.adminPermissions = { support: true };
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    user = await User.findOne({ _id: 2 });
    user.admin = true;
    user.adminPermissions = { support: true };
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    user = await User.findOne({ _id: 3 });
    user.admin = true;
    user.adminPermissions = { support: true, manager: true };
    await user.save();

    // create user to verify
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.22', locale: 'en' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    // start
    fakeLambda.invoke = function (params) {
      const impl = function (resolve, reject) {
        resolve({
          Payload: JSON.stringify({
            id: challengeId,
            imageWidth: 1000,
            imageHeight: 1000,
            areaLeft: 218,
            areaTop: 125,
            areaWidth: 562,
            areaHeight: 750,
            minFaceAreaPercent: 50,
            noseLeft: 412,
            noseTop: 525,
            noseWidth: 20,
            noseHeight: 20,
          }),
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/liveness/start')
      .set('authorization', 0)
      .send({ imageWidth: 1000, imageHeight: 1000 });
    expect(res.status).to.equal(200);

    // post frames
    res = await request(app)
      .post('/v1/liveness/frame')
      .set('authorization', 0)
      .attach('image', validImagePath)
      .query({ challengeId });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/liveness/frame')
      .set('authorization', 0)
      .attach('image', validImagePath)
      .query({ challengeId });
    expect(res.status).to.equal(200);

    // reject
    fakeLambda.invoke = function (params) {
      const impl = function (resolve, reject) {
        resolve({
          Payload: JSON.stringify({
            success: false,
            failure_reason: 'Nose not inside nose box',
          }),
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/liveness/verify')
      .set('authorization', 0)
      .send({ challengeId });
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('rejected');
    expect(res.body.rejectionReason).to.equal('Nose challenge failed.');

    // request manual verification
    res = await request(app)
      .post('/v1/liveness/requestManualVerification')
      .set('authorization', 0)
      .send({ challengeId });
    expect(res.status).to.equal(200);

    // test getting profiles for review
    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    console.log(res.body.users);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('0');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    console.log(res.body.users);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('0');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);
  });

  it('both verify', async () => {
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '0', decision: 'verify' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // already verified
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 2, user: '0', decision: 'verify' })
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // still verified
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    user = await User.findById('0');
    console.log(JSON.stringify(user.verificationHistory,null,2));
    expect(user.verificationHistory.length).to.equal(5);
    expect(user.verificationHistory[0].status).to.equal('rejected');
    expect(user.verificationHistory[0].reason).to.equal('failed nose challenge');
    expect(user.verificationHistory[1].status).to.equal('pending');
    expect(user.verificationHistory[1].reason).to.equal('user requested manual verification for nose challenge');
    expect(user.verificationHistory[2].manualReview.review1.decision).to.equal('verify');
    expect(user.verificationHistory[2].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[2].manualReview.review2.decision).to.equal(null);
    expect(user.verificationHistory[3].status).to.equal('verified');
    expect(user.verificationHistory[3].reason).to.equal('handled manually by admin (first decision in double queue system)');
    expect(user.verificationHistory[3].by).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.decision).to.equal('verify');
    expect(user.verificationHistory[4].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review2.decision).to.equal('verify');
    expect(user.verificationHistory[4].manualReview.review2.reviewedBy).to.equal('2');

    // verifyProfile route should still work
    res = await request(app)
      .patch('/v1/admin/verifyProfile')
      .set('authorization', 3)
      .send({
        user: '0',
        verified: false,
        rejectionReason: 'Incorrect pose',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('Incorrect pose');

    res = await request(app)
      .patch('/v1/admin/verifyProfile')
      .set('authorization', 3)
      .send({
        user: '0',
        verified: true,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');
  });

  it('both reject', async () => {
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '0', decision: 'reject', rejectionReason: 'Poor lighting.' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // immediately rejected
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('Poor lighting.');

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 2, user: '0', decision: 'reject', rejectionReason: 'Nose challenge failed.' })
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // still rejected
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('Poor lighting.');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    user = await User.findById('0');
    console.log(JSON.stringify(user.verificationHistory,null,2));
    expect(user.verificationHistory.length).to.equal(5);
    expect(user.verificationHistory[0].status).to.equal('rejected');
    expect(user.verificationHistory[0].reason).to.equal('failed nose challenge');
    expect(user.verificationHistory[1].status).to.equal('pending');
    expect(user.verificationHistory[1].reason).to.equal('user requested manual verification for nose challenge');
    expect(user.verificationHistory[2].manualReview.review1.decision).to.equal('reject');
    expect(user.verificationHistory[2].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[2].manualReview.review1.rejectionReason).to.equal('Poor lighting.');
    expect(user.verificationHistory[2].manualReview.review2.decision).to.equal(null);
    expect(user.verificationHistory[3].status).to.equal('rejected');
    expect(user.verificationHistory[3].reason).to.equal('handled manually by admin (first decision in double queue system)');
    expect(user.verificationHistory[3].by).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.decision).to.equal('reject');
    expect(user.verificationHistory[4].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.rejectionReason).to.equal('Poor lighting.');
    expect(user.verificationHistory[4].manualReview.review2.decision).to.equal('reject');
    expect(user.verificationHistory[4].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[4].manualReview.review2.rejectionReason).to.equal('Nose challenge failed.');
  });

  it('both ban', async () => {
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '0', decision: 'ban', bannedReason: 'Scammer', bannedNotes: 'scammer' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // immediately rejected and banned
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('Scammer');
    expect(user.bannedNotes).to.equal('scammer (banned during photo verification first review)');
    expect(user.bannedBy).to.equal('1');

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 2, user: '0', decision: 'ban', bannedReason: 'Catfish', bannedNotes: 'catfish' })
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // still rejected and banned
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('Scammer');
    expect(user.bannedNotes).to.equal('scammer (banned during photo verification first review)');
    expect(user.bannedBy).to.equal('1');
    console.log(JSON.stringify(user.verificationHistory,null,2));
    expect(user.verificationHistory.length).to.equal(5);
    expect(user.verificationHistory[0].status).to.equal('rejected');
    expect(user.verificationHistory[0].reason).to.equal('failed nose challenge');
    expect(user.verificationHistory[1].status).to.equal('pending');
    expect(user.verificationHistory[1].reason).to.equal('user requested manual verification for nose challenge');
    expect(user.verificationHistory[2].manualReview.review1.decision).to.equal('ban');
    expect(user.verificationHistory[2].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[2].manualReview.review1.bannedReason).to.equal('Scammer');
    expect(user.verificationHistory[2].manualReview.review1.bannedNotes).to.equal('scammer');
    expect(user.verificationHistory[2].manualReview.review2.decision).to.equal(null);
    expect(user.verificationHistory[3].status).to.equal('rejected');
    expect(user.verificationHistory[3].reason).to.equal('auto rejected due to ban');
    expect(user.verificationHistory[3].by).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.decision).to.equal('ban');
    expect(user.verificationHistory[4].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.bannedReason).to.equal('Scammer');
    expect(user.verificationHistory[4].manualReview.review1.bannedNotes).to.equal('scammer');
    expect(user.verificationHistory[4].manualReview.review2.decision).to.equal('ban');
    expect(user.verificationHistory[4].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[4].manualReview.review2.bannedReason).to.equal('Catfish');
    expect(user.verificationHistory[4].manualReview.review2.bannedNotes).to.equal('catfish');
  });

  it('first verify second reject - final decision verify', async () => {
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '0', decision: 'verify' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // already verified
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 2, user: '0', decision: 'reject', rejectionReason: 'Poor lighting.' })
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // still verified
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('0');
    console.log(res.body.users[0]);

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/reviewFinal/decision')
      .send({ user: '0', decision: 'verify' })
      .set('authorization', 3);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // still verified
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');

    user = await User.findById('0');
    console.log(JSON.stringify(user.verificationHistory,null,2));
    expect(user.verificationHistory.length).to.equal(6);
    expect(user.verificationHistory[0].status).to.equal('rejected');
    expect(user.verificationHistory[0].reason).to.equal('failed nose challenge');
    expect(user.verificationHistory[1].status).to.equal('pending');
    expect(user.verificationHistory[1].reason).to.equal('user requested manual verification for nose challenge');
    expect(user.verificationHistory[2].manualReview.review1.decision).to.equal('verify');
    expect(user.verificationHistory[2].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[2].manualReview.review2.decision).to.equal(null);
    expect(user.verificationHistory[3].status).to.equal('verified');
    expect(user.verificationHistory[3].reason).to.equal('handled manually by admin (first decision in double queue system)');
    expect(user.verificationHistory[3].by).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.decision).to.equal('verify');
    expect(user.verificationHistory[4].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review2.decision).to.equal('reject');
    expect(user.verificationHistory[4].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[4].manualReview.reviewFinal.decision).to.equal(null);
    expect(user.verificationHistory[5].manualReview.review1.decision).to.equal('verify');
    expect(user.verificationHistory[5].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[5].manualReview.review2.decision).to.equal('reject');
    expect(user.verificationHistory[5].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[5].manualReview.reviewFinal.decision).to.equal('verify');
    expect(user.verificationHistory[5].manualReview.reviewFinal.reviewedBy).to.equal('3');
  });

  it('first verify second reject - final decision reject', async () => {
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '0', decision: 'verify' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // already verified
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 2, user: '0', decision: 'reject' })
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // still verified
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('0');
    console.log(res.body.users[0]);

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/reviewFinal/decision')
      .send({ user: '0', decision: 'reject', rejectionReason: 'Nose challenge failed.' })
      .set('authorization', 3);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // now rejected
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('Nose challenge failed.');

    user = await User.findById('0');
    console.log(JSON.stringify(user.verificationHistory,null,2));
    expect(user.verificationHistory.length).to.equal(7);
    expect(user.verificationHistory[0].status).to.equal('rejected');
    expect(user.verificationHistory[0].reason).to.equal('failed nose challenge');
    expect(user.verificationHistory[1].status).to.equal('pending');
    expect(user.verificationHistory[1].reason).to.equal('user requested manual verification for nose challenge');
    expect(user.verificationHistory[2].manualReview.review1.decision).to.equal('verify');
    expect(user.verificationHistory[2].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[2].manualReview.review2.decision).to.equal(null);
    expect(user.verificationHistory[3].status).to.equal('verified');
    expect(user.verificationHistory[3].reason).to.equal('handled manually by admin (first decision in double queue system)');
    expect(user.verificationHistory[3].by).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.decision).to.equal('verify');
    expect(user.verificationHistory[4].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review2.decision).to.equal('reject');
    expect(user.verificationHistory[4].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[4].manualReview.reviewFinal.decision).to.equal(null);
    expect(user.verificationHistory[5].manualReview.review1.decision).to.equal('verify');
    expect(user.verificationHistory[5].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[5].manualReview.review1.rejectionReason).to.equal();
    expect(user.verificationHistory[5].manualReview.review2.decision).to.equal('reject');
    expect(user.verificationHistory[5].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[5].manualReview.review2.rejectionReason).to.equal();
    expect(user.verificationHistory[5].manualReview.reviewFinal.decision).to.equal('reject');
    expect(user.verificationHistory[5].manualReview.reviewFinal.reviewedBy).to.equal('3');
    expect(user.verificationHistory[5].manualReview.reviewFinal.rejectionReason).to.equal('Nose challenge failed.');
    expect(user.verificationHistory[6].status).to.equal('rejected');
    expect(user.verificationHistory[6].reason).to.equal('handled manually by admin (final decision in double queue system)');
    expect(user.verificationHistory[6].by).to.equal('3');
  });

  it('first reject second verify - final decision verify', async () => {
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '0', decision: 'reject' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // already rejected
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 2, user: '0', decision: 'verify' })
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // still rejected
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('0');
    console.log(res.body.users[0]);

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/reviewFinal/decision')
      .send({ user: '0', decision: 'verify' })
      .set('authorization', 3);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // now verified
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');

    user = await User.findById('0');
    console.log(JSON.stringify(user.verificationHistory,null,2));
    expect(user.verificationHistory.length).to.equal(7);
    expect(user.verificationHistory[0].status).to.equal('rejected');
    expect(user.verificationHistory[0].reason).to.equal('failed nose challenge');
    expect(user.verificationHistory[1].status).to.equal('pending');
    expect(user.verificationHistory[1].reason).to.equal('user requested manual verification for nose challenge');
    expect(user.verificationHistory[2].manualReview.review1.decision).to.equal('reject');
    expect(user.verificationHistory[2].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[2].manualReview.review2.decision).to.equal(null);
    expect(user.verificationHistory[3].status).to.equal('rejected');
    expect(user.verificationHistory[3].reason).to.equal('handled manually by admin (first decision in double queue system)');
    expect(user.verificationHistory[3].by).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.decision).to.equal('reject');
    expect(user.verificationHistory[4].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review2.decision).to.equal('verify');
    expect(user.verificationHistory[4].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[4].manualReview.reviewFinal.decision).to.equal(null);
    expect(user.verificationHistory[5].manualReview.review1.decision).to.equal('reject');
    expect(user.verificationHistory[5].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[5].manualReview.review2.decision).to.equal('verify');
    expect(user.verificationHistory[5].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[5].manualReview.reviewFinal.decision).to.equal('verify');
    expect(user.verificationHistory[5].manualReview.reviewFinal.reviewedBy).to.equal('3');
    expect(user.verificationHistory[6].status).to.equal('verified');
    expect(user.verificationHistory[6].reason).to.equal('handled manually by admin (final decision in double queue system)');
    expect(user.verificationHistory[6].by).to.equal('3');
  });

  it('first reject then attempt again before second decision', async () => {
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '0', decision: 'reject' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');

    // now attempt again
    const challengeId = 'attempt2';
    fakeLambda.invoke = function (params) {
      const impl = function (resolve, reject) {
        resolve({
          Payload: JSON.stringify({
            id: challengeId,
            imageWidth: 1000,
            imageHeight: 1000,
            areaLeft: 218,
            areaTop: 125,
            areaWidth: 562,
            areaHeight: 750,
            minFaceAreaPercent: 50,
            noseLeft: 412,
            noseTop: 525,
            noseWidth: 20,
            noseHeight: 20,
          }),
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/liveness/start')
      .set('authorization', 0)
      .send({ imageWidth: 1000, imageHeight: 1000 });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/liveness/frame')
      .set('authorization', 0)
      .attach('image', validImagePath)
      .query({ challengeId });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/liveness/frame')
      .set('authorization', 0)
      .attach('image', validImagePath)
      .query({ challengeId });
    expect(res.status).to.equal(200);

    fakeLambda.invoke = function (params) {
      const impl = function (resolve, reject) {
        resolve({
          Payload: JSON.stringify({
            success: false,
            failure_reason: 'Nose not inside nose box',
          }),
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/liveness/verify')
      .set('authorization', 0)
      .send({ challengeId });
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('rejected');
    expect(res.body.rejectionReason).to.equal('Nose challenge failed.');

    res = await request(app)
      .post('/v1/liveness/requestManualVerification')
      .set('authorization', 0)
      .send({ challengeId });
    expect(res.status).to.equal(200);

    // test getting profiles for review
    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    console.log(res.body.users);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('0');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    console.log(res.body.users);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('0');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    user = await User.findById('0');
    console.log(JSON.stringify(user.verification.manualReview,null,2));
    expect(user.verification.manualReview.review1.reviewedBy).to.equal();
    expect(user.verification.manualReview.review1.reviewedAt).to.equal();
    console.log(JSON.stringify(user.verificationHistory,null,2));
    expect(user.verificationHistory.length).to.equal(6);
    expect(user.verificationHistory[0].status).to.equal('rejected');
    expect(user.verificationHistory[0].reason).to.equal('failed nose challenge');
    expect(user.verificationHistory[1].status).to.equal('pending');
    expect(user.verificationHistory[1].reason).to.equal('user requested manual verification for nose challenge');
    expect(user.verificationHistory[2].manualReview.review1.decision).to.equal('reject');
    expect(user.verificationHistory[2].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[2].manualReview.review2.decision).to.equal(null);
    expect(user.verificationHistory[3].status).to.equal('rejected');
    expect(user.verificationHistory[3].reason).to.equal('handled manually by admin (first decision in double queue system)');
    expect(user.verificationHistory[3].by).to.equal('1');
    expect(user.verificationHistory[4].status).to.equal('rejected');
    expect(user.verificationHistory[4].reason).to.equal('failed nose challenge');
    expect(user.verificationHistory[5].status).to.equal('pending');
    expect(user.verificationHistory[5].reason).to.equal('user requested manual verification for nose challenge');

    // test submitting a decision
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 2, user: '0', decision: 'reject' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
  });

  it('legacy verification route should cancel double queue system', async () => {
    res = await request(app)
      .patch('/v1/admin/verifyProfile')
      .set('authorization', 3)
      .send({
        user: '0',
        verified: false,
        rejectionReason: 'Incorrect pose',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('Incorrect pose');

    // profile should be removed from double queue system
    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);
  });

  it('first ban second reject - final ban', async () => {
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '0', decision: 'ban', bannedReason: 'Scammer', bannedNotes: 'scammer' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // immediately rejected and banned
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('Scammer');
    expect(user.bannedNotes).to.equal('scammer (banned during photo verification first review)');
    expect(user.bannedBy).to.equal('1');

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 2, user: '0', decision: 'reject' })
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // still rejected and banned
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('Scammer');
    expect(user.bannedNotes).to.equal('scammer (banned during photo verification first review)');
    expect(user.bannedBy).to.equal('1');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    console.log(JSON.stringify(res.body.users,null,2));
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('0');
    expect(res.body.users[0].verification.manualReview.review1.reviewedBy).to.eql({ _id: '1', email: '<EMAIL>' });
    expect(res.body.users[0].verification.manualReview.review2.reviewedBy).to.eql({ _id: '2', email: '<EMAIL>' });

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/reviewFinal/decision')
      .send({ user: '0', decision: 'ban', bannedReason: 'Catfish', bannedNotes: 'catfish' })
      .set('authorization', 3);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // still rejected and banned
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('Scammer');
    expect(user.bannedNotes).to.equal('scammer (banned during photo verification first review)');
    expect(user.bannedBy).to.equal('1');
    console.log(JSON.stringify(user.verificationHistory,null,2));
    expect(user.verificationHistory.length).to.equal(6);
    expect(user.verificationHistory[0].status).to.equal('rejected');
    expect(user.verificationHistory[0].reason).to.equal('failed nose challenge');
    expect(user.verificationHistory[1].status).to.equal('pending');
    expect(user.verificationHistory[1].reason).to.equal('user requested manual verification for nose challenge');
    expect(user.verificationHistory[2].manualReview.review1.decision).to.equal('ban');
    expect(user.verificationHistory[2].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[2].manualReview.review2.decision).to.equal(null);
    expect(user.verificationHistory[3].status).to.equal('rejected');
    expect(user.verificationHistory[3].reason).to.equal('auto rejected due to ban');
    expect(user.verificationHistory[3].by).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.decision).to.equal('ban');
    expect(user.verificationHistory[4].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.bannedReason).to.equal('Scammer');
    expect(user.verificationHistory[4].manualReview.review1.bannedNotes).to.equal('scammer');
    expect(user.verificationHistory[4].manualReview.review2.decision).to.equal('reject');
    expect(user.verificationHistory[4].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[4].manualReview.review2.bannedReason).to.equal();
    expect(user.verificationHistory[4].manualReview.review2.bannedNotes).to.equal();
    expect(user.verificationHistory[4].manualReview.reviewFinal.decision).to.equal(null);
    expect(user.verificationHistory[5].manualReview.review1.decision).to.equal('ban');
    expect(user.verificationHistory[5].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[5].manualReview.review1.bannedReason).to.equal('Scammer');
    expect(user.verificationHistory[5].manualReview.review1.bannedNotes).to.equal('scammer');
    expect(user.verificationHistory[5].manualReview.review2.decision).to.equal('reject');
    expect(user.verificationHistory[5].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[5].manualReview.review2.bannedReason).to.equal();
    expect(user.verificationHistory[5].manualReview.review2.bannedNotes).to.equal();
    expect(user.verificationHistory[5].manualReview.reviewFinal.decision).to.equal('ban');
    expect(user.verificationHistory[5].manualReview.reviewFinal.reviewedBy).to.equal('3');
    expect(user.verificationHistory[5].manualReview.reviewFinal.bannedReason).to.equal('Catfish');
    expect(user.verificationHistory[5].manualReview.reviewFinal.bannedNotes).to.equal('catfish');
  });

  it('first ban second verify - final verify', async () => {
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '0', decision: 'ban', bannedReason: 'Scammer', bannedNotes: 'scammer' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // immediately rejected and banned
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('Scammer');
    expect(user.bannedNotes).to.equal('scammer (banned during photo verification first review)');
    expect(user.bannedBy).to.equal('1');

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 2, user: '0', decision: 'verify' })
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // still rejected and banned
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('Scammer');
    expect(user.bannedNotes).to.equal('scammer (banned during photo verification first review)');
    expect(user.bannedBy).to.equal('1');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('0');

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/reviewFinal/decision')
      .send({ user: '0', decision: 'verify' })
      .set('authorization', 3);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // now unbanned and verified
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');
    expect(res.body.user.rejectionReason).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.bannedReason).to.equal();
    expect(user.bannedNotes).to.equal();
    expect(user.bannedBy).to.equal();
    console.log(JSON.stringify(user.verificationHistory,null,2));
    expect(user.verificationHistory.length).to.equal(7);
    expect(user.verificationHistory[0].status).to.equal('rejected');
    expect(user.verificationHistory[0].reason).to.equal('failed nose challenge');
    expect(user.verificationHistory[1].status).to.equal('pending');
    expect(user.verificationHistory[1].reason).to.equal('user requested manual verification for nose challenge');
    expect(user.verificationHistory[2].manualReview.review1.decision).to.equal('ban');
    expect(user.verificationHistory[2].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[2].manualReview.review2.decision).to.equal(null);
    expect(user.verificationHistory[3].status).to.equal('rejected');
    expect(user.verificationHistory[3].reason).to.equal('auto rejected due to ban');
    expect(user.verificationHistory[3].by).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.decision).to.equal('ban');
    expect(user.verificationHistory[4].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review2.decision).to.equal('verify');
    expect(user.verificationHistory[4].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[4].manualReview.reviewFinal.decision).to.equal(null);
    expect(user.verificationHistory[5].manualReview.review1.decision).to.equal('ban');
    expect(user.verificationHistory[5].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[5].manualReview.review1.bannedReason).to.equal('Scammer');
    expect(user.verificationHistory[5].manualReview.review1.bannedNotes).to.equal('scammer');
    expect(user.verificationHistory[5].manualReview.review2.decision).to.equal('verify');
    expect(user.verificationHistory[5].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[5].manualReview.review2.bannedReason).to.equal();
    expect(user.verificationHistory[5].manualReview.review2.bannedNotes).to.equal();
    expect(user.verificationHistory[5].manualReview.reviewFinal.decision).to.equal('verify');
    expect(user.verificationHistory[5].manualReview.reviewFinal.reviewedBy).to.equal('3');
    expect(user.verificationHistory[5].manualReview.reviewFinal.bannedReason).to.equal();
    expect(user.verificationHistory[5].manualReview.reviewFinal.bannedNotes).to.equal();
    expect(user.verificationHistory[6].status).to.equal('verified');
    expect(user.verificationHistory[6].reason).to.equal('handled manually by admin (final decision in double queue system)');
    expect(user.verificationHistory[6].by).to.equal('3');
  });

  it('first ban second verify - final reject', async () => {
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '0', decision: 'ban', bannedReason: 'Scammer', bannedNotes: 'scammer' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // immediately rejected and banned
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('Scammer');
    expect(user.bannedNotes).to.equal('scammer (banned during photo verification first review)');
    expect(user.bannedBy).to.equal('1');

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 2, user: '0', decision: 'verify' })
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // still rejected and banned
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('Scammer');
    expect(user.bannedNotes).to.equal('scammer (banned during photo verification first review)');
    expect(user.bannedBy).to.equal('1');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('0');

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/reviewFinal/decision')
      .send({ user: '0', decision: 'reject' })
      .set('authorization', 3);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // now unbanned but still unverified
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.bannedReason).to.equal();
    expect(user.bannedNotes).to.equal();
    expect(user.bannedBy).to.equal();
    console.log(JSON.stringify(user.verificationHistory,null,2));
    expect(user.verificationHistory.length).to.equal(6);
    expect(user.verificationHistory[0].status).to.equal('rejected');
    expect(user.verificationHistory[0].reason).to.equal('failed nose challenge');
    expect(user.verificationHistory[1].status).to.equal('pending');
    expect(user.verificationHistory[1].reason).to.equal('user requested manual verification for nose challenge');
    expect(user.verificationHistory[2].manualReview.review1.decision).to.equal('ban');
    expect(user.verificationHistory[2].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[2].manualReview.review2.decision).to.equal(null);
    expect(user.verificationHistory[3].status).to.equal('rejected');
    expect(user.verificationHistory[3].reason).to.equal('auto rejected due to ban');
    expect(user.verificationHistory[3].by).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.decision).to.equal('ban');
    expect(user.verificationHistory[4].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review2.decision).to.equal('verify');
    expect(user.verificationHistory[4].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[4].manualReview.reviewFinal.decision).to.equal(null);
    expect(user.verificationHistory[5].manualReview.review1.decision).to.equal('ban');
    expect(user.verificationHistory[5].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[5].manualReview.review2.decision).to.equal('verify');
    expect(user.verificationHistory[5].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[5].manualReview.reviewFinal.decision).to.equal('reject');
    expect(user.verificationHistory[5].manualReview.reviewFinal.reviewedBy).to.equal('3');
  });

  it('should track decision counts for reviewers', async () => {
    let res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '0', decision: 'verify' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    // Make second review decision
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 2, user: '0', decision: 'verify' })
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    // create user to verify
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 4)
      .send({ appVersion: '1.13.22', locale: 'en' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 4)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    const newchallengeId = '1110f6e4-a225-11ed-bf55-d33b0802ecde';

    // start
    fakeLambda.invoke = function (params) {
      const impl = function (resolve, reject) {
        resolve({
          Payload: JSON.stringify({
            id: newchallengeId,
            imageWidth: 1000,
            imageHeight: 1000,
            areaLeft: 218,
            areaTop: 125,
            areaWidth: 562,
            areaHeight: 750,
            minFaceAreaPercent: 50,
            noseLeft: 412,
            noseTop: 525,
            noseWidth: 20,
            noseHeight: 20,
          }),
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/liveness/start')
      .set('authorization', 4)
      .send({ imageWidth: 1000, imageHeight: 1000 });
    expect(res.status).to.equal(200);

    // post frames
    res = await request(app)
      .post('/v1/liveness/frame')
      .set('authorization', 4)
      .attach('image', validImagePath)
      .query({ challengeId: newchallengeId });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/liveness/frame')
      .set('authorization', 4)
      .attach('image', validImagePath)
      .query({ challengeId: newchallengeId });
    expect(res.status).to.equal(200);

    // reject
    fakeLambda.invoke = function (params) {
      const impl = function (resolve, reject) {
        resolve({
          Payload: JSON.stringify({
            success: false,
            failure_reason: 'Nose not inside nose box',
          }),
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/liveness/verify')
      .set('authorization', 4)
      .send({ challengeId: newchallengeId });
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('rejected');
    expect(res.body.rejectionReason).to.equal('Nose challenge failed.');

    // request manual verification
    res = await request(app)
      .post('/v1/liveness/requestManualVerification')
      .set('authorization', 4)
      .send({ challengeId: newchallengeId });
    expect(res.status).to.equal(200);

    // create another decision from support user 1
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '4', decision: 'verify' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    // Check that accuracy records were created
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const reviewer1Record = await SupportAgentAccuracy.findOne({
      date: today,
      supportAgentId: '1',
    });
    expect(reviewer1Record).to.not.equal(null);
    expect(reviewer1Record.totalDecisions).to.equal(2);
    expect(reviewer1Record.incorrectDecisions).to.equal(0);

    const reviewer2Record = await SupportAgentAccuracy.findOne({
      date: today,
      supportAgentId: '2',
    });
    expect(reviewer2Record).to.not.equal(null);
    expect(reviewer2Record.totalDecisions).to.equal(1);
    expect(reviewer2Record.incorrectDecisions).to.equal(0);
  });

  it('should track incorrect decisions when final review disagrees with initial reviews', async () => {
    let res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '0', decision: 'ban', bannedReason: 'Fake profile', bannedNotes: 'suspicious' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 2, user: '0', decision: 'verify' })
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    // Final reviewer decides to 'verify' - disagreeing with reviewer 1
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/reviewFinal/decision')
      .send({ user: '0', decision: 'verify' })
      .set('authorization', 3);
    expect(res.status).to.equal(200);

    // Check accuracy records
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const reviewer1Record = await SupportAgentAccuracy.findOne({
      date: today,
      supportAgentId: '1',
    });
    expect(reviewer1Record).to.not.equal(null);
    expect(reviewer1Record.totalDecisions).to.equal(1);
    expect(reviewer1Record.incorrectDecisions).to.equal(1);

    const reviewer2Record = await SupportAgentAccuracy.findOne({
      date: today,
      supportAgentId: '2',
    });
    expect(reviewer1Record).to.not.equal(null);
    expect(reviewer2Record.totalDecisions).to.equal(1);
    expect(reviewer2Record.incorrectDecisions).to.equal(0);
  });

  it('should return accurate leaderboard data based on actual review decisions across multiple days', async () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Day 1 (today): Create some decisions
    let res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '0', decision: 'ban', bannedReason: 'Suspicious', bannedNotes: 'test' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 2, user: '0', decision: 'verify' })
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/reviewFinal/decision')
      .send({ user: '0', decision: 'verify' })
      .set('authorization', 3);
    expect(res.status).to.equal(200);

    // Day 2: Manually create accuracy records for 3 days ago to simulate historical data
    const threeDaysAgo = new Date(today);
    threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);

    await SupportAgentAccuracy.create([
      {
        supportAgentId: '1',
        supportAgentEmail: '<EMAIL>',
        date: threeDaysAgo,
        totalDecisions: 5,
        incorrectDecisions: 1,
      },
      {
        supportAgentId: '2',
        supportAgentEmail: '<EMAIL>',
        date: threeDaysAgo,
        totalDecisions: 3,
        incorrectDecisions: 0,
      },
    ]);

    // Day 3: Create accuracy records for 6 days ago (should be included)
    const sixDaysAgo = new Date(today);
    sixDaysAgo.setDate(sixDaysAgo.getDate() - 6);

    await SupportAgentAccuracy.create([
      {
        supportAgentId: '1',
        supportAgentEmail: '<EMAIL>',
        date: sixDaysAgo,
        totalDecisions: 4,
        incorrectDecisions: 2,
      },
      {
        supportAgentId: '2',
        supportAgentEmail: '<EMAIL>',
        date: sixDaysAgo,
        totalDecisions: 8,
        incorrectDecisions: 0,
      },
    ]);

    // Day 4: Create accuracy records for 8 days ago (should be EXCLUDED from 7-day window)
    const eightDaysAgo = new Date(today);
    eightDaysAgo.setDate(eightDaysAgo.getDate() - 8);

    await SupportAgentAccuracy.create([
      {
        supportAgentId: '1',
        supportAgentEmail: '<EMAIL>',
        date: eightDaysAgo,
        totalDecisions: 10,
        incorrectDecisions: 0,
      },
      {
        supportAgentId: '2',
        supportAgentEmail: '<EMAIL>',
        date: eightDaysAgo,
        totalDecisions: 15,
        incorrectDecisions: 0,
      },
    ]);

    // Test leaderboard route
    res = await request(app)
      .get('/v1/admin/verifyProfileNew/leaderboard')
      .set('authorization', 3);
    expect(res.status).to.equal(200);

    const leaderboard = res.body.leaderboard;
    const reviewer1 = leaderboard.find(r => r.supportAgentId === '1');
    const reviewer2 = leaderboard.find(r => r.supportAgentId === '2');

    // Verify Reviewer 1's aggregated stats (today + 3 days ago + 6 days ago)
    // Total decisions: 1 (today) + 5 (3 days ago) + 4 (6 days ago) = 10
    // Incorrect decisions: 1 (today, disagreed with final) + 1 (3 days ago) + 2 (6 days ago) = 4
    // Accuracy: (10-4)/10 * 100 = 60%
    expect(reviewer1.totalDecisions).to.equal(10);
    expect(reviewer1.incorrectDecisions).to.equal(4);
    expect(reviewer1.accuracyRate).to.equal(60);

    // Verify Reviewer 2's aggregated stats (today + 3 days ago + 6 days ago)
    // Total decisions: 1 (today) + 3 (3 days ago) + 8 (6 days ago) = 12
    // Incorrect decisions: 0 (today, agreed with final) + 0 (3 days ago) + 0 (6 days ago) = 0
    // Accuracy: 100%
    expect(reviewer2.totalDecisions).to.equal(12);
    expect(reviewer2.incorrectDecisions).to.equal(0);
    expect(reviewer2.accuracyRate).to.equal(100);

    // Verify sorting: Reviewer2 should be first (100% accuracy, 12 decisions)
    // Then Reviewer1 (60% accuracy, 10 decisions)
    expect(leaderboard[0].supportAgentId).to.equal('2');
    expect(leaderboard[1].supportAgentId).to.equal('1');

    // Verify that 8-day-old data is excluded by checking the totals don't include it
    // If 8-day data was included, reviewer1 would have 20 total decisions and reviewer2 would have 27
    expect(reviewer1.totalDecisions).to.not.equal(20);
    expect(reviewer2.totalDecisions).to.not.equal(27);
  });
});
