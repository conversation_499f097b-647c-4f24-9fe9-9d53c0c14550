const aws = require('aws-sdk');
const multer = require('multer');
const multerS3 = require('multer-s3');
const cryptoRandomString = require('crypto-random-string');
const mime = require('mime-types');
const path = require('path');
const { badRequestError } = require('./http-errors');
const bunny = require('../lib/bunny');
const crypto = require('crypto');

const AWS_S3_BUCKET = process.env.AWS_S3_BUCKET || 'MOCK_S3_BUCKET';

const s3 = new aws.S3({
  accessKeyId: process.env.AWS_KEY,
  secretAccessKey: process.env.AWS_SECRET,
  region: 'us-east-1',
});

const storage = multerS3({
  s3,
  bucket: AWS_S3_BUCKET,
  contentType: multerS3.AUTO_CONTENT_TYPE,
  key(req, file, cb) {
    const folder = req.user._id;
    const randomString = cryptoRandomString({ length: 32 });
    const fileName = `${folder}/${Date.now().toString()}${randomString}${path.extname(file.originalname)}`;
    cb(null, fileName);
  },
});

function fileFilter(req, file, cb) {
  const mimetype = mime.lookup(file.originalname);
  const fileExtension = path.extname(file.originalname).toLowerCase();
  console.log(`Evaluating file for upload to s3: ${
    JSON.stringify(file, null, 2)
  }, file mimetype: ${mimetype
  }, file extension: ${fileExtension}`);
  if (
    !mimetype
    || !mimetype.includes('jpeg')
    && !mimetype.includes('jpg')
    && !mimetype.includes('png')
  ) {
    req.fileValidationError = badRequestError('Only jpg and png images are allowed');
    return cb(null, false);
  }
  if (
    !(fileExtension == '.jpeg')
    && !(fileExtension == '.jpg')
    && !(fileExtension == '.png')
  ) {
    req.fileValidationError = badRequestError('Only jpg and png file extensions are allowed');
    return cb(null, false);
  }
  cb(null, true);
}

function fileFilterAudio(req, file, cb) {
  const mimetype = mime.lookup(file.originalname);
  const fileExtension = path.extname(file.originalname).toLowerCase();
  console.log(`Evaluating file for upload to s3: ${
    JSON.stringify(file, null, 2)
  }, file mimetype: ${mimetype
  }, file extension: ${fileExtension}`);
  if (
    !mimetype
    || !mimetype.includes('mpeg')
    && !mimetype.includes('aac')
    && !mimetype.includes('wav')
  ) {
    req.fileValidationError = badRequestError('Only mp3, aac and wav files are allowed');
    return cb(null, false);
  }
  if (
    !(fileExtension == '.mp3')
    && !(fileExtension == '.aac')
    && !(fileExtension == '.wav')
  ) {
    req.fileValidationError = badRequestError('Only mp3, aac and wav file extensions are allowed');
    return cb(null, false);
  }
  cb(null, true);
}

function fileFilterVideo(req, file, cb) {
  const mimetype = mime.lookup(file.originalname);
  const fileExtension = path.extname(file.originalname).toLowerCase();
  console.log(`Evaluating file for upload to s3: ${
    JSON.stringify(file, null, 2)
  }, file mimetype: ${mimetype
  }, file extension: ${fileExtension}`);
  if (
    !mimetype
    || !mimetype.includes('video')
  ) {
    req.fileValidationError = badRequestError('Only mp4, mov and avi files are allowed');
    return cb(null, false);
  }
  if (
    !(fileExtension == '.mp4')
    && !(fileExtension == '.webm')
    && !(fileExtension == '.avi')
    && !(fileExtension == '.flv')
    && !(fileExtension == '.mkv')
    && !(fileExtension == '.mpg')
    && !(fileExtension == '.wmv')
    && !(fileExtension == '.mov')
  ) {
    req.fileValidationError = badRequestError('Only mp4, mov and avi file extensions are allowed');
    return cb(null, false);
  }
  cb(null, true);
}

function getFileType(file) {
  const mimetype = mime.lookup(file.originalname);
  const fileExtension = path.extname(file.originalname).toLowerCase();
  let fileType;
  if (mimetype && (mimetype.includes('jpeg') || mimetype.includes('jpg') || mimetype.includes('png'))
      && ['.jpeg', '.jpg', '.png'].includes(fileExtension)) {
    fileType = 'image';
  }
  if (mimetype && mimetype.includes('video')
      && ['.mp4', '.webm', '.avi', '.flv', '.mkv', '.mpg', 'wmv', '.mov'].includes(fileExtension)) {
    fileType = 'video';
  }
  return fileType;
}

function fileFilterImageOrVideo(req, file, cb) {
  const mimetype = mime.lookup(file.originalname);
  const fileExtension = path.extname(file.originalname).toLowerCase();
  console.log(`Evaluating file for upload to s3: ${
    JSON.stringify(file, null, 2)
  }, file mimetype: ${mimetype
  }, file extension: ${fileExtension}`);
  if (mimetype && (mimetype.includes('jpeg') || mimetype.includes('jpg') || mimetype.includes('png'))
      && ['.jpeg', '.jpg', '.png'].includes(fileExtension)) {
    req.fileType = 'image';
    return cb(null, true);
  }
  if (mimetype && mimetype.includes('video')
      && ['.mp4', '.webm', '.avi', '.flv', '.mkv', '.mpg', 'wmv', '.mov'].includes(fileExtension)) {
    req.fileType = 'video';
    return cb(null, true);
  }

  req.fileValidationError = badRequestError('Invalid file');
  return cb(null, false);
}

const imageSizeLimit = 50 * 1000 * 1000;
const videoSizeLimit = 1000 * 1000 * 1000;
const audioSizeLimit = 15 * 1000 * 1000;

const multerUpload = () => multer({
  storage,
  fileFilter,
  limits: {
    fileSize: imageSizeLimit,
  },
});

const uploadProfileVideo = multer({
  storage,
  fileFilter: fileFilterVideo,
  limits: {
    fileSize: videoSizeLimit,
  },
});

const profileVerificationImageStorage = multerS3({
  s3,
  bucket: AWS_S3_BUCKET,
  contentType: multerS3.AUTO_CONTENT_TYPE,
  key(req, file, cb) {
    const folder = `${req.user._id}/verification`;
    const randomString = cryptoRandomString({ length: 32 });
    const fileName = `${folder}/${Date.now().toString()}${randomString}${path.extname(file.originalname)}`;
    cb(null, fileName);
  },
});

const uploadProfileVerificationImage = multer({
  storage: profileVerificationImageStorage,
  fileFilter,
  limits: {
    fileSize: imageSizeLimit,
  },
});

const livenessImageStorage = multerS3({
  s3,
  bucket: AWS_S3_BUCKET,
  contentType: multerS3.AUTO_CONTENT_TYPE,
  key(req, file, cb) {
    const folder = `${req.user._id}/liveness/${req.query.challengeId}`;
    const randomString = cryptoRandomString({ length: 32 });
    const fileName = `${folder}/${Date.now().toString()}${randomString}${path.extname(file.originalname)}`;
    cb(null, fileName);
  },
});

const uploadLivenessImage = multer({
  storage: livenessImageStorage,
  fileFilter,
  limits: {
    fileSize: imageSizeLimit,
  },
});

const storyImageStorage = multerS3({
  s3,
  bucket: AWS_S3_BUCKET,
  contentType: multerS3.AUTO_CONTENT_TYPE,
  key(req, file, cb) {
    const folder = `${req.user._id}/stories/${req.story._id}`;
    const randomString = cryptoRandomString({ length: 32 });
    const fileName = `${folder}/${Date.now().toString()}${randomString}${path.extname(file.originalname)}`;
    cb(null, fileName);
  },
});

const uploadStoryImage = multer({
  storage: storyImageStorage,
  fileFilter,
  limits: {
    fileSize: imageSizeLimit,
  },
});

const uploadStoryImageOrVideo = multer({
  storage: storyImageStorage,
  fileFilterImageOrVideo,
  limits: {
    fileSize: videoSizeLimit,
  },
});

const databaseProfileImageStorage = multerS3({
  s3,
  bucket: AWS_S3_BUCKET,
  contentType: multerS3.AUTO_CONTENT_TYPE,
  key(req, file, cb) {
    const folder = 'database/profiles';
    const randomString = cryptoRandomString({ length: 32 });
    const fileName = `${folder}/${Date.now().toString()}${randomString}${path.extname(file.originalname)}`;
    cb(null, fileName);
  },
});

const uploadDatabaseProfileImage = multer({
  storage: databaseProfileImageStorage,
  fileFilter,
  limits: {
    fileSize: imageSizeLimit,
  },
});

const chatImageStorage = multerS3({
  s3,
  bucket: AWS_S3_BUCKET,
  contentType: multerS3.AUTO_CONTENT_TYPE,
  key(req, file, cb) {
    const folder = `chats/${req.chat._id}`;
    const randomString = cryptoRandomString({ length: 32 });
    const fileName = `${folder}/${Date.now().toString()}${randomString}${path.extname(file.originalname)}`;
    cb(null, fileName);
  },
});

const uploadChatImage = multer({
  storage: chatImageStorage,
  fileFilter,
  limits: {
    fileSize: imageSizeLimit,
  },
});

const uploadChatAudio = multer({
  storage: chatImageStorage,
  fileFilter: fileFilterAudio,
  limits: {
    fileSize: audioSizeLimit,
  },
});

const uploadChatVideo = multer({
  storage: chatImageStorage,
  fileFilter: fileFilterVideo,
  limits: {
    fileSize: videoSizeLimit,
  },
});

const questionImageStorage = multerS3({
  s3,
  bucket: AWS_S3_BUCKET,
  contentType: multerS3.AUTO_CONTENT_TYPE,
  key(req, file, cb) {
    const folder = `questions/${req.question._id}`;
    const randomString = cryptoRandomString({ length: 32 });
    const fileName = `${folder}/${Date.now().toString()}${randomString}${path.extname(file.originalname)}`;
    cb(null, fileName);
  },
});

const uploadQuestionImage = multer({
  storage: questionImageStorage,
  fileFilter,
  limits: {
    fileSize: imageSizeLimit,
    limit: 20
  },
});

const uploadQuestionVideo = multer({
  storage: questionImageStorage,
  fileFilter: fileFilterVideo,
  limits: {
    fileSize: videoSizeLimit,
  },
});

const uploadQuestionAudio = multer({
  storage: questionImageStorage,
  fileFilter: fileFilterAudio,
  limits: {
    fileSize: audioSizeLimit,
  },
});

const commentImageStorage = multerS3({
  s3,
  bucket: AWS_S3_BUCKET,
  contentType: multerS3.AUTO_CONTENT_TYPE,
  key(req, file, cb) {
    const folder = `comments/${req.comment._id}`;
    const randomString = cryptoRandomString({ length: 32 });
    const fileName = `${folder}/${Date.now().toString()}${randomString}${path.extname(file.originalname)}`;
    cb(null, fileName);
  },
});

const uploadCommentImage = multer({
  storage: commentImageStorage,
  fileFilter,
  limits: {
    fileSize: imageSizeLimit,
  },
});

const uploadCommentVideo = multer({
  storage: commentImageStorage,
  fileFilter: fileFilterVideo,
  limits: {
    fileSize: videoSizeLimit,
  },
});

const uploadCommentAudio = multer({
  storage: commentImageStorage,
  fileFilter: fileFilterAudio,
  limits: {
    fileSize: audioSizeLimit,
  },
});

const uploadDescriptionAudio = multer({
  storage,
  fileFilter: fileFilterAudio,
  limits: {
    fileSize: audioSizeLimit,
  },
});

const translationImageStorage = multerS3({
  s3,
  bucket: AWS_S3_BUCKET,
  contentType: multerS3.AUTO_CONTENT_TYPE,
  key(req, file, cb) {
    const folder = `translations/${req.translation._id}`;
    const randomString = cryptoRandomString({ length: 32 });
    const fileName = `${folder}/${Date.now().toString()}${randomString}${path.extname(file.originalname)}`;
    cb(null, fileName);
  },
});

const uploadTranslationImage = multer({
  storage: translationImageStorage,
  fileFilter,
  limits: {
    fileSize: imageSizeLimit,
  },
});

async function listDirectory(dir) {
  const listParams = {
    Bucket: AWS_S3_BUCKET,
    Prefix: dir,
  };

  const listedObjects = await s3.listObjectsV2(listParams).promise();
  const keys = listedObjects.Contents.map(x => x.Key);
  return keys;
}

async function emptyS3Directory(bucket, dir) {
  const listParams = {
    Bucket: bucket,
    Prefix: dir,
  };

  const listedObjects = await s3.listObjectsV2(listParams).promise();

  if (listedObjects.Contents.length === 0) return;

  const deleteParams = {
    Bucket: bucket,
    Delete: { Objects: [] },
  };

  listedObjects.Contents.forEach(({ Key }) => {
    deleteParams.Delete.Objects.push({ Key });
    bunny.purge(Key);
  });

  console.log(`Deleting from s3: ${JSON.stringify(deleteParams, null, 2)}`);

  await s3.deleteObjects(deleteParams).promise();

  if (listedObjects.IsTruncated) await emptyS3Directory(dir);
}

async function deletePicture(key) {
  const params = {
    Bucket: AWS_S3_BUCKET,
    Key: key,
  };
  await s3.deleteObject(params).promise();
  bunny.purge(key);
  emptyS3Directory(AWS_S3_BUCKET, `${key}/`);
}

async function copyToBanned(key, userId) {

  const randomString = cryptoRandomString({ length: 32 });
  const newPath = `banned/${userId}/${Date.now().toString()}${randomString}${path.extname(key)}`;
  console.log(`copying ${key} to ${newPath}`);
  try {
    await s3.copyObject({
      Bucket: AWS_S3_BUCKET,
      CopySource: AWS_S3_BUCKET + '/' + key,
      Key: newPath,
    }).promise();
    return newPath;
  }
  catch (err) {
    console.log(`error AWS S3 copyObject to path ${newPath}: `, err);
  }
  return;
}

async function uploadUserImage(userId, data, extension) {

  const randomString = cryptoRandomString({ length: 32 });
  const path = `${userId}/${Date.now().toString()}${randomString}.${extension}`;
  try {
    await s3.putObject({
      Bucket: AWS_S3_BUCKET,
      Key: path,
      Body: data,
    }).promise();
    return path;
  }
  catch (err) {
    console.log(`error AWS S3 putObject to path ${path}: `, err);
  }
  return;
}

async function uploadYotiVerificationImage(userId, img) {
  return new Promise((resolve) => {
    try {
      const buffer = Buffer.from(img.replace(/^data:image\/\w+;base64,/, ""), 'base64');

      if (buffer.length > imageSizeLimit) {
        console.log('Image size exceeds the limit');
        return resolve(null);
      }

      const randomString = cryptoRandomString({ length: 32 });
      const fileName = `${userId}/verification/${Date.now().toString()}${randomString}.jpg`;

      const params = {
        Bucket: AWS_S3_BUCKET,
        Key: fileName,
        Body: buffer,
        ContentEncoding: 'base64',
      };

      s3.upload(params, (err, data) => {
        if (err) {
          console.log('Failed to upload Yoti verification image to S3:', err);
          resolve(null);
        } else {
          resolve(data.Key);
        }
      });
    } catch (err) {
      console.log('Failed to upload Yoti verification image to S3:', err);
      resolve(null);
    }
  });
}

async function generateHashFromS3(key) {
  try {
    const response = await s3.getObject({ Bucket: AWS_S3_BUCKET, Key: key }).promise();
    const hash = crypto.createHash('sha256').update(response.Body).digest('hex');
    return hash;
  } catch (error) {
    console.log(`Error during banned file hash generation for ${key}:`, error.message);
    return null;
  }
}

async function deleteS3ObjectsInBulk(keys) {
  if (!Array.isArray(keys) || keys.length === 0) {
    return;
  }

  const MAX_KEYS = 1000; // S3 allows a maximum of 1000 keys per delete request

  for (let i = 0; i < keys.length; i += MAX_KEYS) {
    const batch = keys.slice(i, i + MAX_KEYS);
    const params = {
      Bucket: AWS_S3_BUCKET,
      Delete: {
        Objects: batch.map((key) => ({ Key: key })),
      },
    };

    try {
      const result = await s3.deleteObjects(params).promise();

      if (result.Errors && result.Errors.length > 0) {
        const failedKeys = result.Errors.map((err) => ({
          key: err.Key,
          error: `${err.Code}: ${err.Message}`,
        }));

        console.log('[S3BulkDeleteError]', JSON.stringify({
          bucket: AWS_S3_BUCKET,
          failedCount: failedKeys.length,
          failedKeys,
        }));
      }
    } catch (err) {
      console.log('[S3BulkDeleteError]', JSON.stringify({
        bucket: AWS_S3_BUCKET,
        failedCount: batch.length,
        failedKeys: batch.map(key => ({
          key,
          error: err.message,
        })),
      }));
    }
  }
}

module.exports = {
  s3,
  multerUpload,
  uploadProfileVideo,
  uploadProfileVerificationImage,
  uploadChatImage,
  uploadChatAudio,
  uploadChatVideo,
  uploadQuestionImage,
  uploadQuestionVideo,
  uploadQuestionAudio,
  uploadCommentImage,
  uploadCommentVideo,
  uploadCommentAudio,
  uploadDescriptionAudio,
  uploadDatabaseProfileImage,
  uploadTranslationImage,
  uploadLivenessImage,
  uploadStoryImage,
  uploadStoryImageOrVideo,
  AWS_S3_BUCKET,
  emptyS3Directory,
  deletePicture,
  copyToBanned,
  getFileType,
  listDirectory,
  uploadUserImage,
  uploadYotiVerificationImage,
  generateHashFromS3,
  deleteS3ObjectsInBulk,
};
