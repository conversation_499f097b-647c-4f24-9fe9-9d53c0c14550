const basicLib = require('../lib/basic');

const allNiches = [
  'anime',
  'gaming',
  'lifting',
  'kpop',
  'movies',
  'music',
  'art',

  // alternate spellings
  'weightlifting',
  'game',
  'movie',
];

function formatNiche(niche) {
  if (niche == 'game') {
    return 'gaming';
  }
  if (niche == 'weightlifting') {
    return 'lifting';
  }
  if (niche == 'movie') {
    return 'movies';
  }
  return niche;
}

function getNicheFromCampaign(user) {

  let niche;

  // handle influencer campaigns
  if (!niche && user?.kochava?.network?.toLowerCase()?.startsWith('influencer')) {
    niche = allNiches.find(word => basicLib.containsEntireWord(user.kochava?.network?.toLowerCase(), word));
  }

  // handle all other campaigns
  if (!niche) {
    niche = allNiches.find(word => basicLib.containsEntireWord(user.kochava?.partner_campaign_name?.toLowerCase(), word));
  }

  if (niche) {
    return formatNiche(niche);
  }
}

module.exports = { getNicheFromCampaign };
