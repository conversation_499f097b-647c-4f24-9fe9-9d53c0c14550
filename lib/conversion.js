const { conversionEvent } = require("./constants");
const EventEmitter = require("events");
const conversionEmitter = new EventEmitter();
const AppsFlyerClient = require("./appsflyer/AppsFlyerClient")


// // should not to called directly
// async function listenSignup(data){
//   try {
//    // add sign up event action here
    
//   } catch (error) {
//     console.log("Error sending conversion data :", error);
//   }
// }

// conversionEmitter.on(conversionEvent.SIGNUP, async (data) => await listenSignup(data));

// should not to called directly
async function listenPurchase(data) {

  try {
    console.log('listenPurchase called')
    return await emitAppsflyer('af_purchase', data)
  } catch (error) {
    console.error("Error sending conversion data :", error);
  }
}

async function emitAppsflyer(eventName, data){
    
    try {
      const {user, productId, currency, price, qty = 1} = data
      if(!user.versionAtLeast('1.13.87')){
        console.log(`user app version is below 1.13.87, purchase event send by FE`)
        return
      }

      if(process.env.NODE_ENV == 'prod'){
        console.log(`only send s2s event on test and beta env`)
        return
      }

      if(!user.appsflyer?.appsflyer_id || !user.os || !price ){
        
        console.log(`appsflyer s2s not send, appsflyer_id: ${user.appsflyer?.appsflyer_id}, os: ${user.os}, price: ${price}`)
        return
      }

      let eventValue = ""
      if(eventName == 'af_purchase'){
          eventValue = {
          af_currency: currency,
          af_revenue: price,
          af_quantity: qty,
          af_validated: true,
        }
      }
      
      const params = {
        eventName,
        eventValue,
        appsflyer_id: user.appsflyer.appsflyer_id,
        os: user.osVersion
      };
      console.log('event params: ', params)
      const emit = await AppsFlyerClient.sendEvent(user.os, params)
      console.log(`done send appsFlyer event ${eventName}, user ${user._id}, product ${productId} `);
      return true
    } catch (error) {
      console.log('Fail Emit appsflyer event, error: ', error)
      return false
    }
    

}

conversionEmitter.on(conversionEvent.PURCHASE, (data) => listenPurchase(data));


if (process.env.NODE_ENV === 'test'){
  module.exports = {conversionEmitter, listenPurchase}; // export private module for testing
}else{
  module.exports = {conversionEmitter};
}

  