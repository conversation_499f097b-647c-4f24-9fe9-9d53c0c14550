const moment = require('moment');
const basic = require('../lib/basic');
const constants = require('../lib/constants');
const lookup = require('country-code-lookup');
const fs = require('fs');
const { parse } = require('csv-parse/sync');


/*
CSV INPUT niche_onboarding_videos.csv:
anime,female_gaming,gaming
be,be,be
bg,bg,bg
cs,cs,cs

PARSED:
[
  { anime: 'be', 'female_gaming': 'be', gaming: 'be' },
  { anime: 'bg', 'female_gaming': 'bg', gaming: 'bg' },
  { anime: 'cs', 'female_gaming': 'cs', gaming: 'cs' },
]

OUTPUT:
{
  anime: [ 'be', 'bg', 'cs' ],
  female_gaming: [ 'be', 'bg', 'cs' ],
  gaming: [ 'be', 'bg', 'cs' ],
}
*/
let nicheVideos;
{
  const pathToCsv = `${__dirname}/niche_onboarding_videos.csv`;
  const input = fs.readFileSync(pathToCsv);
  const records = parse(input, {
    columns: true,
    skip_empty_lines: true,
    trim: true,
  });
  const map = {};
  for (const record of records) {
    for (const [key, value] of Object.entries(record)) {
      if (key && value) {
        if (!map[key]) {
          map[key] = [];
        }
        map[key].push(value);
      }
    }
  }
  nicheVideos = map;
}

const englishAfricaCountries = ['NG','ET','ZA','TZ','KE','SD','UG','GH','CM','MW','ZM','ZW','SS','RW','BI','SL','LR','NA','LS','BW','GM','MU','SZ','SC'];
const frenchAfricaCountries = ['BJ','BF','BI','CM','KM','CD','CG','DJ','GA','GN','GQ','MG','ML','NE','CF','RW','SN','SC','TD','TG'];

const batch1 = [
  'ar',
  'ca',
  'cs',
  'da',
  'de',
  'el',
  'en_AU',
  'en_GB',
  'en_SG',
  'en_US',
  'en_african',
  'en',
  'fi',
  'fil',
  'fr_african',
  'fr',
  'he',
  'hi',
  'hu',
  'id',
  'iw',
  'ja',
  'nl',
  'no',
  'pl',
  'pt',
  'ro',
  'sv',
  'th',
  'tr',
  'zh_Hans',
  'zh_Hant_TW',
  'zh_Hant',
];

const batch2 = [
  'pt_BR',
  'hy',
  'sq',
  'sl',
];

const v1Locales = batch1.concat(batch2);

function getAvailableVideoLocales() {
  return v1Locales;
}

function getCountryLocale(user) {
  let locale = user.locale;
  let country = lookup.byCountry(user.signupCountry)?.iso2;

  if (user.countryLocale) {
    if (user.countryLocale == 'zh_Hans' || user.countryLocale == 'zh_Hant') {
      locale = user.countryLocale;
    }
    else {
      const split = user.countryLocale.split('_');
      if (split.length == 3) {
        if (user.countryLocale.includes('zh_Hans') || user.countryLocale.includes('zh_Hant')) {
          locale = split[0] + '_' + split[1];
          country = split[2];
        } else {
          locale = split[0];
          country = split[1] + '_' + split[2];
        }
      }
      else if (split.length == 2) {
        locale = split[0];
        country = split[1];
      }
    }
  }

  if (locale == 'zh-Hans') {
    locale = 'zh_Hans';
  }
  if (locale == 'zh-Hant') {
    locale = 'zh_Hant';
  }
  if (locale == 'nn' || locale == 'nb') {
    locale = 'no';
  }

  let countryLocale = `${locale}_${country}`;
  if (locale == 'en' && englishAfricaCountries.includes(country)) {
    countryLocale = 'en_african';
  }
  if (locale == 'fr' && frenchAfricaCountries.includes(country)) {
    countryLocale = 'fr_african';
  }

  return { locale, country, countryLocale }
}

function getOnboardingVideoConfig(user) {
  const { locale, country } = getCountryLocale(user);

  if (!user.versionAtLeast('1.13.12')) {
    return false;
  }
  if (user.appVersion == '1.13.12' && !batch1.includes(locale)) {
    return false;
  }
  if (user.versionAtLeast('1.13.13') && !v1Locales.includes(locale)) {
    return false;
  }

  if (locale == 'en') {
    if (['US','AU','GB', 'SG'].includes(country) || englishAfricaCountries.includes(country)) {
      return true;
    }
    return true;
  }
  if (locale == 'fr') {
    if (['CA'].includes(country) || frenchAfricaCountries.includes(country)) {
      return false;
    }
    return true;
  }
  if (['de','el','hu','ro','nl','pl','tr','he','sv','th','ca'].includes(locale)) {
    return true;
  }
  if (['ar','cs','id','ja','zh_Hans','zh_Hant','da','fil','hi','no','da','fi'].includes(locale)) {
    return false;
  }
  if (locale == 'pt') {
    if (country == 'BR') {
      if (user.versionAtLeast('1.13.13')) {
        return true;
      }
      return false;
    }
    return true;
  }
  return null;
}

const v2Locales = [
  'af',
  'bn',
  'ca',
  'cs',
  'da',
  'de',
  'el',
  'en_AU',
  'en_GB',
  'en_SG',
  'en_US',
  'en_african',
  'en',
  'es',
  'es_AR',
  'es_MX',
  'et',
  'fi',
  'fil',
  'fr',
  'hi',
  'hu',
  'hy',
  'id',
  'ja',
  'ka',
  'kk',
  'nl',
  'pl',
  'pt',
  'pt_BR',
  'ro',
  'ru',
  'sk',
  'sl',
  'sq',
  'sv',
  'th',
  'tr',
  'vi',
  'zh_Hans',
  'zh_Hant_TW',
];

function getAvailableVideoV2Locales() {
  return v2Locales;
}

function getOnboardingVideoV2Config(user) {
  const { locale, country, countryLocale } = getCountryLocale(user);

  if (!user.versionAtLeast('1.13.15')) {
    return false;
  }
  if (!v2Locales.includes(locale) && !v2Locales.includes(countryLocale)) {
    return false;
  }

  if (['es', 'fr', 'ca', 'da', 'el', 'sv', 'de', 'sk', 'sl', 'et', 'sq'].includes(locale)) {
    return true;
  }
  if (['en', 'cs', 'hu', 'id', 'nl', 'pl', 'pt', 'ro', 'ru', 'tr', 'fi', 'fil', 'ja', 'th', 'vi', 'zh_Hans', 'zh_Hant', 'ka', 'hi', 'hy', 'bn'].includes(locale)) {
    return false;
  }

  return null;
}

const friendsLocales = [
  'en_US',
  'ja',
];

function getAvailableVideoFriendsLocales() {
  return friendsLocales;
}

function getOnboardingVideoFriendsConfig(user) {
  return false;
}

const friendsV2Locales = [
  'en_US',
  'en',
  'fr',
  'ca',
  'da',
  'tr',
  'ms',
  'pl',
  'de',
  'no',
  'es',
  'sl',
  'cs',
  'fi',
  'he',
  'lt',
  'zh_Hant',
  'zh_Hans',
  'fil',
  'pt',
  'sv',
  'ar',
  'gl',
  'id',
  'it',
  'ja',
  'nl',
  'ro',
  'ru',
  'sk',
  'sq',
  'sr',
  'uk',
  'bg',
  'bn',
  'ko',
];
function getOnboardingVideoFriendsV2Config(user) {
  const { locale, country, countryLocale } = getCountryLocale(user);

  if (!user.versionAtLeast('1.13.17')) {
    return false;
  }
  if (!friendsV2Locales.includes(locale) && !friendsV2Locales.includes(countryLocale)) {
    return false;
  }
  if (locale == 'en') {
    if (['AU','NZ','GB','IN','SG'].includes(country)) {
      return false;
    }
    return true;
  }
  if (['es', 'id', 'it', 'ru', 'tr', 'he', 'ja', 'sv', 'da', 'fi', 'sk', 'bg', 'ko', 'lt', 'sl', 'sr'].includes(locale)) {
    return true;
  }
  if (['fr', 'pt', 'de', 'ar', 'ms', 'uk', 'cs', 'nl', 'pl', 'ro'].includes(locale)) {
    return false;
  }
  return null;
}

const womenDatingLocales = [
  'en_US',
  'af',
  'ar',
  'az',
  'bg',
  'ca',
  'cs',
  'da',
  'de',
  'en',
  'es',
  'et',
  'fil',
  'hy',
  'id',
  'it',
  'ja',
  'kk',
  'ko',
  'lt',
  'ms',
  'nl',
  'no',
  'pl',
  'pt',
  'pt_BR',
  'ro',
  'ru',
  'sk',
  'sl',
  'sr',
  'tr',
  'uk',
  'zh_Hans',
  'zh_Hant',
];
function getOnboardingVideoWomenDatingConfig(user) {
  const { locale, country, countryLocale } = getCountryLocale(user);

  if (!user.versionAtLeast('1.13.17')) {
    return false;
  }
  if (!womenDatingLocales.includes(locale) && !womenDatingLocales.includes(countryLocale)) {
    return false;
  }
  if (['cs', 'de', 'en', 'es', 'pl', 'pt', 'ro', 'tr', 'id', 'ko', 'ru', 'sk', 'sr', 'ja', 'lt'].includes(locale)) {
    return true;
  }
  if (['zh_Hans', 'bg', 'da', 'ms', 'nl', 'sl', 'uk', 'et', 'it', 'ar'].includes(locale)) {
    return false;
  }
  return null;
}

const menDatingLocales = [
  'en_US',
  'en',
];
function getOnboardingVideoMenDatingConfig(user) {
  const { locale, country, countryLocale } = getCountryLocale(user);

  if (!user.versionAtLeast('1.13.17')) {
    return false;
  }
  if (!menDatingLocales.includes(locale) && !menDatingLocales.includes(countryLocale)) {
    return false;
  }
  if (['en_african','en_AU','en_GB','en_IN','en_NZ','en_SG'].includes(countryLocale)) {
    return false;
  }
  return null;
}

function assignOnboardingVideoExperimentConfig(user) {
  {
    const config = getOnboardingVideoConfig(user);
    if (config == null) {
      key = 'show_onboarding_video_all_languages';
      if (user.config[key] === undefined) {
        user.config[key] = basic.assignConfig(0.5);
      }
    }
  }

  {
    const config = getOnboardingVideoV2Config(user);
    if (config == null) {
      key = 'onboarding_video_dating_v2';
      if (user.config[key] === undefined) {
        user.config[key] = basic.assignConfig(0.5);
      }
    }
  }

  {
    const config = getOnboardingVideoFriendsConfig(user);
    if (config == null) {
      key = 'onboarding_video_friends';
      if (user.config[key] === undefined) {
        user.config[key] = basic.assignConfig(0.5);
      }
    }
  }

  {
    const config = getOnboardingVideoFriendsV2Config(user);
    if (config == null) {
      key = 'onboarding_video_friends_v2';
      if (user.config[key] === undefined) {
        user.config[key] = basic.assignConfig(0.5);
      }
    }
  }

  {
    const config = getOnboardingVideoWomenDatingConfig(user);
    if (config == null) {
      key = 'onboarding_video_women_dating';
      if (user.config[key] === undefined) {
        user.config[key] = basic.assignConfig(0.5);
      }
    }
  }

  {
    const config = getOnboardingVideoMenDatingConfig(user);
    if (config == null) {
      key = 'onboarding_video_men_dating';
      if (user.config[key] === undefined) {
        user.config[key] = basic.assignConfig(0.5);
      }
    }
  }
}

function getOnboardingVideoUrl(user) {
  const { locale, country, countryLocale } = getCountryLocale(user);
  const color = user.darkMode ? 'black' : 'white';

  function findNicheVideo(niche) {
    const localeFound = nicheVideos[niche].find(x => x == countryLocale) || nicheVideos[niche].find(x => x == locale);
    if (localeFound) {
      return `${constants.IMAGE_DOMAIN}onboarding_video_${niche}/${localeFound}_${color}.mp4`;
    }
  }

  // partnerCampaign takes precedence over interests
  if (user.partnerCampaign == 'art') {
    const nicheVideo = findNicheVideo('art');
    if (nicheVideo) return nicheVideo;
  }
  if (user.partnerCampaign == 'lifting') {
    const nicheVideo = findNicheVideo('lifting');
    if (nicheVideo) return nicheVideo;
  }
  if (user.partnerCampaign == 'anime') {
    const nicheVideo = findNicheVideo('anime');
    if (nicheVideo) return nicheVideo;
  }
  if (user.partnerCampaign == 'gaming' && user.gender == 'male') {
    const nicheVideo = findNicheVideo('gaming');
    if (nicheVideo) return nicheVideo;
  }
  if (user.partnerCampaign == 'gaming' && user.gender == 'female') {
    const nicheVideo = findNicheVideo('gaming_women');
    if (nicheVideo) return nicheVideo;
  }
  if (user.events.added_interest_anime) {
    const nicheVideo = findNicheVideo('anime');
    if (nicheVideo) return nicheVideo;
  }
  if (user.events.added_interest_gaming && user.gender == 'male') {
    const nicheVideo = findNicheVideo('gaming');
    if (nicheVideo) return nicheVideo;
  }
  if (user.events.added_interest_gaming && user.gender == 'female') {
    const nicheVideo = findNicheVideo('gaming_women');
    if (nicheVideo) return nicheVideo;
  }

  if (user.preferences.dating.length == 0 && user.preferences.friends.length > 0) {
    let friends_v2 = getOnboardingVideoFriendsV2Config(user);
    if (friends_v2 == null) {
      friends_v2 = user.config.onboarding_video_friends_v2;
    }
    if (friends_v2) {
      if (friendsV2Locales.includes(countryLocale)) {
        return `${constants.IMAGE_DOMAIN}onboarding_video_friends_v2/${countryLocale}_${color}.mp4`;
      }
      if (friendsV2Locales.includes(locale)) {
        return `${constants.IMAGE_DOMAIN}onboarding_video_friends_v2/${locale}_${color}.mp4`;
      }
    }
  }

  if (user.preferences.dating.length == 0) {
    return null;
  }

  if (user.gender == 'female') {
    let config = getOnboardingVideoWomenDatingConfig(user);
    if (config == null) {
      config = user.config.onboarding_video_women_dating;
    }
    if (config) {
      if (womenDatingLocales.includes(countryLocale)) {
        return `${constants.IMAGE_DOMAIN}onboarding_video_women_dating/${countryLocale}_${color}.mp4`;
      }
      if (womenDatingLocales.includes(locale)) {
        return `${constants.IMAGE_DOMAIN}onboarding_video_women_dating/${locale}_${color}.mp4`;
      }
    }
  }

  if (user.gender == 'male') {
    let config = getOnboardingVideoMenDatingConfig(user);
    if (config == null) {
      config = user.config.onboarding_video_men_dating;
    }
    if (config) {
      if (menDatingLocales.includes(countryLocale)) {
        return `${constants.IMAGE_DOMAIN}onboarding_video_men_dating/${countryLocale}_${color}.mp4`;
      }
      if (menDatingLocales.includes(locale)) {
        return `${constants.IMAGE_DOMAIN}onboarding_video_men_dating/${locale}_${color}.mp4`;
      }
    }
  }

  let v2 = getOnboardingVideoV2Config(user);
  if (v2 == null) {
    v2 = user.config.onboarding_video_dating_v2;
  }
  if (v2) {
    if (v2Locales.includes(countryLocale)) {
      return `${constants.IMAGE_DOMAIN}onboarding_video_v2/${countryLocale}.mp4`;
    }
    if (v2Locales.includes(locale)) {
      return `${constants.IMAGE_DOMAIN}onboarding_video_v2/${locale}.mp4`;
    }
  }

  let v1 = getOnboardingVideoConfig(user);
  if (v1 == null) {
    v1 = user.config.show_onboarding_video_all_languages;
  }
  if (v1) {
    if (v1Locales.includes(countryLocale)) {
      return `${constants.IMAGE_DOMAIN}onboarding_video/${countryLocale}_${color}.mp4`;
    }
    if (v1Locales.includes(locale)) {
      return `${constants.IMAGE_DOMAIN}onboarding_video/${locale}_${color}.mp4`;
    }
  }
  return null;
}


module.exports = {
  getOnboardingVideoConfig,
  assignOnboardingVideoExperimentConfig,
  getAvailableVideoLocales,
  getAvailableVideoV2Locales,
  getOnboardingVideoV2Config,
  getAvailableVideoFriendsLocales,
  getOnboardingVideoFriendsConfig,
  getOnboardingVideoUrl,
};
